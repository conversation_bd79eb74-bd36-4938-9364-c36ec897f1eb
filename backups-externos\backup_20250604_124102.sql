--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: DeviceType; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."DeviceType" AS ENUM (
    'NOKIA',
    'HUAWEI',
    'MIKROTIK',
    'DMOS',
    'GENERIC'
);


ALTER TYPE public."DeviceType" OWNER TO postgres;

--
-- Name: OS; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."OS" AS ENUM (
    'LINUX',
    'WINDOWS'
);


ALTER TYPE public."OS" OWNER TO postgres;

--
-- Name: Role; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."Role" AS ENUM (
    'ADMIN',
    'USER'
);


ALTER TYPE public."Role" OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: Command; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."Command" (
    id text NOT NULL,
    name text NOT NULL,
    command text NOT NULL,
    description text,
    "serverId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "isLatest" boolean DEFAULT true NOT NULL,
    "previousVersionId" text,
    version integer DEFAULT 1 NOT NULL,
    "order" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public."Command" OWNER TO postgres;

--
-- Name: CommandHistory; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."CommandHistory" (
    id text NOT NULL,
    "userId" text NOT NULL,
    "serverId" text NOT NULL,
    "commandId" text,
    result text,
    status integer DEFAULT 0 NOT NULL,
    "executedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "commandName" text,
    "commandText" text
);


ALTER TABLE public."CommandHistory" OWNER TO postgres;

--
-- Name: CommandTemplate; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."CommandTemplate" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    "isPublic" boolean DEFAULT false NOT NULL,
    "userId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."CommandTemplate" OWNER TO postgres;

--
-- Name: CommandTemplateItem; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."CommandTemplateItem" (
    id text NOT NULL,
    name text NOT NULL,
    command text NOT NULL,
    description text,
    "templateId" text NOT NULL,
    "order" integer DEFAULT 0 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."CommandTemplateItem" OWNER TO postgres;

--
-- Name: Server; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."Server" (
    id text NOT NULL,
    name text NOT NULL,
    host text NOT NULL,
    port integer DEFAULT 22 NOT NULL,
    username text NOT NULL,
    password text,
    "privateKey" text,
    os public."OS" DEFAULT 'LINUX'::public."OS" NOT NULL,
    "userId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "deviceType" public."DeviceType" DEFAULT 'GENERIC'::public."DeviceType" NOT NULL
);


ALTER TABLE public."Server" OWNER TO postgres;

--
-- Name: ServerGroup; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."ServerGroup" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    color text DEFAULT '#3B82F6'::text,
    "userId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ServerGroup" OWNER TO postgres;

--
-- Name: ServerGroupMember; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."ServerGroupMember" (
    id text NOT NULL,
    "groupId" text NOT NULL,
    "serverId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ServerGroupMember" OWNER TO postgres;

--
-- Name: ServerUser; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."ServerUser" (
    id text NOT NULL,
    "userId" text NOT NULL,
    "serverId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ServerUser" OWNER TO postgres;

--
-- Name: User; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."User" (
    id text NOT NULL,
    email text NOT NULL,
    name text NOT NULL,
    password text NOT NULL,
    role public."Role" DEFAULT 'USER'::public."Role" NOT NULL,
    active boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."User" OWNER TO postgres;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO postgres;

--
-- Data for Name: Command; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."Command" (id, name, command, description, "serverId", "createdAt", "updatedAt", "isLatest", "previousVersionId", version, "order") FROM stdin;
72c90281-2958-4b07-a1fa-83418d7e2362	Verificar Status	systemctl status	Verifica o status dos serviços do sistema	be7ea689-35f4-4c76-a098-d2a356828476	2025-05-31 13:00:28.469	2025-05-31 13:00:28.469	t	\N	1	0
c5db07d6-f6b5-413e-82d7-a1d2c949082b	Verificar Status	systemctl status\npwd\nls	Verifica o status dos serviços do sistema	cc4fc2cc-dcb9-4775-af67-83c91e78baf8	2025-06-04 00:09:15.217	2025-06-04 12:13:49.732	t	\N	1	0
\.


--
-- Data for Name: CommandHistory; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."CommandHistory" (id, "userId", "serverId", "commandId", result, status, "executedAt", "createdAt", "commandName", "commandText") FROM stdin;
6b4dbc75-d58e-47ec-92f5-a904d002b6b9	89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	be7ea689-35f4-4c76-a098-d2a356828476	72c90281-2958-4b07-a1fa-83418d7e2362	Todos os serviços estão funcionando normalmente	0	2025-05-31 13:00:28.512	2025-05-31 13:00:28.512	\N	\N
\.


--
-- Data for Name: CommandTemplate; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."CommandTemplate" (id, name, description, "isPublic", "userId", "createdAt", "updatedAt") FROM stdin;
6f97f70a-9ba2-4312-b7bb-06e6f715a252	Template de Manutenção	Comandos básicos para manutenção do servidor	t	89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	2025-05-31 13:00:28.483	2025-05-31 13:00:28.483
1b6bd583-2f41-493d-b655-23543779906f	teste	teste	f	89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	2025-06-04 12:10:23.152	2025-06-04 12:10:35.617
dd79aa04-e15e-4510-8361-7b1822eaa6b7	teste 22		f	89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	2025-06-04 12:15:35.221	2025-06-04 12:15:35.221
\.


--
-- Data for Name: CommandTemplateItem; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."CommandTemplateItem" (id, name, command, description, "templateId", "order", "createdAt", "updatedAt") FROM stdin;
260606c0-0c59-4e2f-9297-029ea352eca3	Verificar Memória	free -h	Mostra o uso de memória	6f97f70a-9ba2-4312-b7bb-06e6f715a252	1	2025-05-31 13:00:28.483	2025-05-31 13:00:28.483
0f51acc8-a25b-4608-9d4b-a743b5609122	Verificar Disco	df -h	Mostra o uso de disco	6f97f70a-9ba2-4312-b7bb-06e6f715a252	2	2025-05-31 13:00:28.483	2025-05-31 13:00:28.483
20bc9c0a-ca0e-4dda-baa4-55053b0dc7f8	Verificar Status	pwd	Verifica o status dos serviços do sistema	1b6bd583-2f41-493d-b655-23543779906f	0	2025-06-04 12:10:35.617	2025-06-04 12:10:35.617
e4fdff72-2b6e-43c4-b764-11b375ce4096	Verificar Status	systemctl status\npwd\nls	Verifica o status dos serviços do sistema	dd79aa04-e15e-4510-8361-7b1822eaa6b7	0	2025-06-04 12:15:35.221	2025-06-04 12:15:35.221
\.


--
-- Data for Name: Server; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."Server" (id, name, host, port, username, password, "privateKey", os, "userId", "createdAt", "updatedAt", "deviceType") FROM stdin;
be7ea689-35f4-4c76-a098-d2a356828476	Servidor de Desenvolvimento	localhost	22	dev	senha123	\N	LINUX	89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	2025-05-31 13:00:28.455	2025-05-31 13:00:28.455	GENERIC
cc4fc2cc-dcb9-4775-af67-83c91e78baf8	Servidor de Desenvolvimento 2	localhost	22	dev	senha123		LINUX	89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	2025-06-04 00:09:15.217	2025-06-04 12:13:49.724	GENERIC
\.


--
-- Data for Name: ServerGroup; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."ServerGroup" (id, name, description, color, "userId", "createdAt", "updatedAt") FROM stdin;
bbccc523-51e9-4d54-9240-3f58f0c79629	teste	teste	#F97316	89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	2025-06-04 00:08:04.233	2025-06-04 00:08:04.233
\.


--
-- Data for Name: ServerGroupMember; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."ServerGroupMember" (id, "groupId", "serverId", "createdAt", "updatedAt") FROM stdin;
f4040840-9179-450e-a49d-50d53fcf7b16	bbccc523-51e9-4d54-9240-3f58f0c79629	cc4fc2cc-dcb9-4775-af67-83c91e78baf8	2025-06-04 00:36:54.638	2025-06-04 00:36:54.638
\.


--
-- Data for Name: ServerUser; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."ServerUser" (id, "userId", "serverId", "createdAt", "updatedAt") FROM stdin;
f109c372-d2f3-42f9-a94d-281df4ff744b	be784478-6897-4755-aeca-f95bfa784a45	be7ea689-35f4-4c76-a098-d2a356828476	2025-05-31 13:00:28.502	2025-05-31 13:00:28.502
\.


--
-- Data for Name: User; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."User" (id, email, name, password, role, active, "createdAt", "updatedAt") FROM stdin;
89f64a0a-3f28-4fe0-89aa-f4156bd2aad0	<EMAIL>	Administrador	$2a$08$YPqOpKQ8PX.ghzbHbbyzcuuqMQMc4DAZTnA/RepaEXWS0cxGQq8MS	ADMIN	t	2025-05-31 13:00:28.414	2025-05-31 13:00:28.414
be784478-6897-4755-aeca-f95bfa784a45	<EMAIL>	Usuário Teste	$2a$08$ismv/IQazXq6F1h0YUjWeuEgAwv56Wn3RkokzX213y3DIDofGAXKW	USER	t	2025-05-31 13:00:28.442	2025-05-31 13:00:28.442
\.


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
697b5f18-6bca-46c7-9e88-89350586a5a1	29386f0ccd12bf600a1c6a89493f55d373ffdb06ca1bae92bc1f434cb8d782a2	2025-05-31 12:45:57.419744+00	20250410140515_init	\N	\N	2025-05-31 12:45:57.04598+00	1
9f0d4b82-1c4b-496d-a706-8a6d113060d4	019c4396ca0ed1c569bd1501d878c3b5d681cf6d9f8f028e49a02e1465392e2d	2025-05-31 12:45:57.518995+00	20250412000422_initial	\N	\N	2025-05-31 12:45:57.502546+00	1
22850181-1b9d-42fb-9599-1c8c262b22f4	6e27f268623eec83a052fc2ab8aa78a61fbb310c5d73175aab2594c1039b9d56	2025-05-31 12:45:57.680657+00	20250415165733_add_missing_columns	\N	\N	2025-05-31 12:45:57.602625+00	1
e62c104c-1ecb-4b25-aaf7-2ce9a5f7e81e	38cfb488f2565ca5614576e259d0dda37779fffb92bfdd1f95415f1649d99051	2025-05-31 12:45:57.782078+00	20250416222535_add_command_backup_fields	\N	\N	2025-05-31 12:45:57.754606+00	1
93ae3344-2e26-49f5-ab89-ae2c791c9736	9ec1b12175239407246705acdd61a37cf090129f5af459342e9573cc04d7ac05	2025-05-31 12:45:57.839073+00	20250416230000_make_command_id_nullable	\N	\N	2025-05-31 12:45:57.823544+00	1
1b491f66-e758-4279-9e64-1bda5a5c8287	907cda550d5459bb8c08c4e00e1f02d064b357d9b4105a76f1794af53ef334a2	2025-05-31 12:45:57.893064+00	20250501010323_update_command_history_schema	\N	\N	2025-05-31 12:45:57.868867+00	1
09475ab8-4d52-4767-be1f-41d8e2c50e68	8f33cef9938dcf37b1051b13bf5023c9ee885664ee5737fd90a8820ca6f2e389	2025-05-31 12:45:57.934956+00	20250525145526_add_device_type_to_server	\N	\N	2025-05-31 12:45:57.923174+00	1
d33e7adc-93c3-4a94-b5b8-d0a85155a618	efee22f021d4e5b6535c1444c106acd41efc6b634a12dcd9965ecf8684a28ec8	2025-05-31 12:45:58.123002+00	20250525162701_add_server_groups	\N	\N	2025-05-31 12:45:58.003012+00	1
\.


--
-- Name: CommandHistory CommandHistory_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandHistory"
    ADD CONSTRAINT "CommandHistory_pkey" PRIMARY KEY (id);


--
-- Name: CommandTemplateItem CommandTemplateItem_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandTemplateItem"
    ADD CONSTRAINT "CommandTemplateItem_pkey" PRIMARY KEY (id);


--
-- Name: CommandTemplate CommandTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandTemplate"
    ADD CONSTRAINT "CommandTemplate_pkey" PRIMARY KEY (id);


--
-- Name: Command Command_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Command"
    ADD CONSTRAINT "Command_pkey" PRIMARY KEY (id);


--
-- Name: ServerGroupMember ServerGroupMember_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerGroupMember"
    ADD CONSTRAINT "ServerGroupMember_pkey" PRIMARY KEY (id);


--
-- Name: ServerGroup ServerGroup_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerGroup"
    ADD CONSTRAINT "ServerGroup_pkey" PRIMARY KEY (id);


--
-- Name: ServerUser ServerUser_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerUser"
    ADD CONSTRAINT "ServerUser_pkey" PRIMARY KEY (id);


--
-- Name: Server Server_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Server"
    ADD CONSTRAINT "Server_pkey" PRIMARY KEY (id);


--
-- Name: User User_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."User"
    ADD CONSTRAINT "User_pkey" PRIMARY KEY (id);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: Command_serverId_isLatest_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "Command_serverId_isLatest_idx" ON public."Command" USING btree ("serverId", "isLatest");


--
-- Name: ServerGroupMember_groupId_serverId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "ServerGroupMember_groupId_serverId_key" ON public."ServerGroupMember" USING btree ("groupId", "serverId");


--
-- Name: ServerGroup_userId_name_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "ServerGroup_userId_name_key" ON public."ServerGroup" USING btree ("userId", name);


--
-- Name: ServerUser_userId_serverId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "ServerUser_userId_serverId_key" ON public."ServerUser" USING btree ("userId", "serverId");


--
-- Name: User_email_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "User_email_key" ON public."User" USING btree (email);


--
-- Name: CommandHistory CommandHistory_commandId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandHistory"
    ADD CONSTRAINT "CommandHistory_commandId_fkey" FOREIGN KEY ("commandId") REFERENCES public."Command"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: CommandHistory CommandHistory_serverId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandHistory"
    ADD CONSTRAINT "CommandHistory_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES public."Server"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: CommandHistory CommandHistory_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandHistory"
    ADD CONSTRAINT "CommandHistory_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: CommandTemplateItem CommandTemplateItem_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandTemplateItem"
    ADD CONSTRAINT "CommandTemplateItem_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public."CommandTemplate"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: CommandTemplate CommandTemplate_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."CommandTemplate"
    ADD CONSTRAINT "CommandTemplate_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Command Command_previousVersionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Command"
    ADD CONSTRAINT "Command_previousVersionId_fkey" FOREIGN KEY ("previousVersionId") REFERENCES public."Command"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: Command Command_serverId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Command"
    ADD CONSTRAINT "Command_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES public."Server"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ServerGroupMember ServerGroupMember_groupId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerGroupMember"
    ADD CONSTRAINT "ServerGroupMember_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES public."ServerGroup"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ServerGroupMember ServerGroupMember_serverId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerGroupMember"
    ADD CONSTRAINT "ServerGroupMember_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES public."Server"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ServerGroup ServerGroup_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerGroup"
    ADD CONSTRAINT "ServerGroup_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ServerUser ServerUser_serverId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerUser"
    ADD CONSTRAINT "ServerUser_serverId_fkey" FOREIGN KEY ("serverId") REFERENCES public."Server"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ServerUser ServerUser_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ServerUser"
    ADD CONSTRAINT "ServerUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Server Server_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Server"
    ADD CONSTRAINT "Server_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- PostgreSQL database dump complete
--

