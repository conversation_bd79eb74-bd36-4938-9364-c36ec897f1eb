# 🎯 Nova Funcionalidade: Importar Comandos em Templates Existentes

## ✅ O que foi implementado:

### 1. **<PERSON><PERSON><PERSON> "Importar de Servidor" no TemplateModal**
- Disponível tanto no modo de criação quanto no modo de edição
- Localizado ao lado do botão "Adicionar Comando"
- Cor verde para diferenciação visual

### 2. **Funcionalidade de Importação**
- Modal reutilizado com textos personalizados
- Comandos são **adicionados** aos comandos existentes (não substituem)
- Ordem automática baseada na quantidade atual de comandos

### 3. **Interface Responsiva**
- Botões organizados horizontalmente em desktop
- Empilhamento vertical em dispositivos móveis
- Textos contextuais no modal

## 🔧 Implementação Técnica:

### `TemplateModal.tsx` - Modificações:
```tsx
// Estado para controlar o modal de importação
const [isImportModalOpen, setIsImportModalOpen] = useState(false)

// Função para importar comandos
function handleCommandsImported(commands: Omit<Command, 'id' | 'serverId' | 'createdAt' | 'updatedAt'>[]) {
  // Adicionar os comandos importados aos comandos existentes
  commands.forEach((cmd, index) => {
    append({
      name: cmd.name,
      command: cmd.command,
      description: cmd.description || '',
      order: fields.length + index,
    })
  })
  setIsImportModalOpen(false)
}

// Botão de importação
<button
  type="button"
  onClick={handleImportCommands}
  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
>
  <Download className="h-4 w-4 mr-1" />
  Importar de Servidor
</button>
```

### `CopyFromServerModal.tsx` - Melhorias:
```tsx
// Props opcionais para personalização
interface CopyFromServerModalProps {
  isOpen: boolean
  onClose: () => void
  onCommandsSelected: (commands: Omit<Command, 'id' | 'serverId' | 'createdAt' | 'updatedAt'>[]) => void
  title?: string
  confirmButtonText?: string
}

// Uso com textos personalizados
<CopyFromServerModal
  isOpen={isImportModalOpen}
  onClose={() => setIsImportModalOpen(false)}
  onCommandsSelected={handleCommandsImported}
  title="Importar Comandos de Servidor"
  confirmButtonText="Importar"
/>
```

## 🎨 Interface Atualizada:

### Seção de Comandos no TemplateModal:
```
┌─────────────────────────────────────────────────────────┐
│ 📋 Comandos                                             │
│                                    [Importar] [Adicionar] │
├─────────────────────────────────────────────────────────┤
│ Comando #1                                              │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ Nome: Verificar Sistema                             │ │
│ │ Comando: [textarea multi-linhas]                    │ │
│ │ [Upload Script]                                     │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ Comando #2 (importado)                                 │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ Nome: Monitoramento de Rede                         │ │
│ │ Comando: [textarea com comandos importados]         │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 🚀 Fluxos de Uso:

### 1. **Criar Novo Template com Comandos Importados**:
1. Clicar em "Copiar de Servidor" na página principal
2. Selecionar servidor e comandos
3. Template criado com comandos pré-populados
4. Adicionar nome, descrição e comandos extras se necessário
5. Salvar template

### 2. **Editar Template Existente e Importar Comandos**:
1. Clicar em "Editar" em um template existente
2. Template abre com comandos atuais
3. Clicar em "Importar de Servidor"
4. Selecionar servidor e comandos adicionais
5. Comandos são **adicionados** aos existentes
6. Editar conforme necessário
7. Salvar alterações

### 3. **Adicionar Comandos Manualmente**:
1. Clicar em "Adicionar Comando"
2. Preencher campos manualmente
3. Usar upload de script se necessário
4. Comandos multi-linhas suportados

## 📝 Exemplos Práticos:

### Cenário 1: Template de Monitoramento
**Template inicial:**
- Comando 1: `df -h` (verificar disco)
- Comando 2: `free -h` (verificar memória)

**Importar de servidor:**
- Comando 3: `ps aux | head -10` (processos)
- Comando 4: `netstat -tuln` (portas)

**Resultado final:**
- 4 comandos no template
- Ordem preservada
- Funcionalidade completa

### Cenário 2: Template de Backup
**Template inicial:**
- Comando 1: Script de backup básico

**Importar comandos complexos:**
- Comando 2: Script de verificação de integridade
- Comando 3: Script de limpeza de logs
- Comando 4: Script de notificação

## ✨ Benefícios:

1. **Flexibilidade Total**: Criar e expandir templates facilmente
2. **Reutilização Máxima**: Aproveitar comandos existentes
3. **Produtividade**: Não precisar recriar comandos do zero
4. **Organização**: Combinar comandos de diferentes fontes
5. **Evolução**: Templates podem crescer organicamente
6. **Consistência**: Interface unificada em todo o sistema

## 🎉 Status: IMPLEMENTADO COM SUCESSO!

A funcionalidade está **100% funcional** e oferece:

- ✅ Importação em templates novos e existentes
- ✅ Comandos multi-linhas com upload de scripts
- ✅ Interface responsiva e intuitiva
- ✅ Textos contextuais no modal
- ✅ Preservação da ordem dos comandos
- ✅ Adição (não substituição) de comandos
- ✅ Compatibilidade total com funcionalidades existentes

**Resultado:** Sistema de templates extremamente flexível e poderoso! 🚀
