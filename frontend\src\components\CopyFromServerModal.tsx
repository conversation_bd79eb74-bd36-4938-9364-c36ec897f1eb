import { Fragment, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useQuery } from '@tanstack/react-query'
import { getServersForCopy } from '../services/api'
import { Command } from '../types/server'
import { Server, Terminal, Check, X, Copy } from 'lucide-react'

interface CopyFromServerModalProps {
  isOpen: boolean
  onClose: () => void
  onCommandsSelected: (commands: Omit<Command, 'id' | 'serverId' | 'createdAt' | 'updatedAt'>[]) => void
  title?: string
  confirmButtonText?: string
}

export default function CopyFromServerModal({
  isOpen,
  onClose,
  onCommandsSelected,
  title = "Copiar Comandos de Servidor",
  confirmButtonText = "Copiar"
}: CopyFromServerModalProps) {
  const [selectedServerId, setSelectedServerId] = useState<string>('')
  const [selectedCommands, setSelectedCommands] = useState<Set<string>>(new Set())

  const { data: servers = [], isLoading } = useQuery({
    queryKey: ['serversForCopy'],
    queryFn: getServersForCopy,
    enabled: isOpen,
  })

  const selectedServer = servers.find(server => server.id === selectedServerId)

  function handleServerSelect(serverId: string) {
    setSelectedServerId(serverId)
    setSelectedCommands(new Set()) // Limpar comandos selecionados ao trocar de servidor
  }

  function handleCommandToggle(commandId: string) {
    const newSelected = new Set(selectedCommands)
    if (newSelected.has(commandId)) {
      newSelected.delete(commandId)
    } else {
      newSelected.add(commandId)
    }
    setSelectedCommands(newSelected)
  }

  function handleSelectAll() {
    if (!selectedServer) return
    
    if (selectedCommands.size === selectedServer.commands.length) {
      // Se todos estão selecionados, desmarcar todos
      setSelectedCommands(new Set())
    } else {
      // Selecionar todos
      setSelectedCommands(new Set(selectedServer.commands.map(cmd => cmd.id)))
    }
  }

  function handleConfirm() {
    if (!selectedServer || selectedCommands.size === 0) return

    const commandsToAdd = selectedServer.commands
      .filter(cmd => selectedCommands.has(cmd.id))
      .map((cmd, index) => ({
        name: cmd.name,
        command: cmd.command,
        description: cmd.description,
        order: index,
      }))

    onCommandsSelected(commandsToAdd)
    handleClose()
  }

  function handleClose() {
    setSelectedServerId('')
    setSelectedCommands(new Set())
    onClose()
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={handleClose}>
        <Transition.Child
          as="div"
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
          className="fixed inset-0 bg-black bg-opacity-25"
        />

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as="div"
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <Copy className="h-6 w-6 text-blue-500" />
                    <h3 className="text-lg font-medium text-gray-900">
                      {title}
                    </h3>
                  </div>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                <div className="space-y-6">
                  {/* Seleção de Servidor */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Selecione um servidor
                    </label>
                    {isLoading ? (
                      <div className="flex justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                      </div>
                    ) : servers.length === 0 ? (
                      <p className="text-gray-500 text-sm">Nenhum servidor encontrado</p>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-48 overflow-y-auto">
                        {servers.map((server) => (
                          <button
                            key={server.id}
                            onClick={() => handleServerSelect(server.id)}
                            className={`p-3 border rounded-lg text-left transition-colors ${
                              selectedServerId === server.id
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              <Server className="h-4 w-4 text-gray-500" />
                              <div>
                                <p className="font-medium text-gray-900">{server.name}</p>
                                <p className="text-sm text-gray-500">{server.host}</p>
                                <p className="text-xs text-gray-400">
                                  {server.commands.length} comando{server.commands.length !== 1 ? 's' : ''}
                                </p>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Seleção de Comandos */}
                  {selectedServer && (
                    <div>
                      <div className="flex justify-between items-center mb-3">
                        <label className="block text-sm font-medium text-gray-700">
                          Comandos do servidor "{selectedServer.name}"
                        </label>
                        <button
                          onClick={handleSelectAll}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {selectedCommands.size === selectedServer.commands.length ? 'Desmarcar todos' : 'Selecionar todos'}
                        </button>
                      </div>
                      
                      {selectedServer.commands.length === 0 ? (
                        <p className="text-gray-500 text-sm">Este servidor não possui comandos</p>
                      ) : (
                        <div className="space-y-2 max-h-64 overflow-y-auto border border-gray-200 rounded-lg p-3">
                          {selectedServer.commands.map((command) => (
                            <label
                              key={command.id}
                              className="flex items-start gap-3 p-2 rounded hover:bg-gray-50 cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={selectedCommands.has(command.id)}
                                onChange={() => handleCommandToggle(command.id)}
                                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                  <Terminal className="h-4 w-4 text-gray-500 flex-shrink-0" />
                                  <p className="font-medium text-gray-900 truncate">{command.name}</p>
                                </div>
                                <p className="text-sm text-gray-600 font-mono bg-gray-100 px-2 py-1 rounded mt-1">
                                  {command.command}
                                </p>
                                {command.description && (
                                  <p className="text-xs text-gray-500 mt-1">{command.description}</p>
                                )}
                              </div>
                            </label>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 mt-6">
                  <button
                    onClick={handleClose}
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleConfirm}
                    disabled={selectedCommands.size === 0}
                    className="inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    {confirmButtonText} {selectedCommands.size} comando{selectedCommands.size !== 1 ? 's' : ''}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
